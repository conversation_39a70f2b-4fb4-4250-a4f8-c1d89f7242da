@echo off
echo ========================================
echo   Build Chamego Virtual - Producao
echo ========================================
echo.
echo Configuracao atual: PRODUCTION
echo API Endpoint: http://198.1.195.126:5032/api
echo.

echo Limpando builds anteriores...
flutter clean

echo Obtendo dependencias...
flutter pub get

echo.
echo ========================================
echo   Opcoes de Build Disponiveis
echo ========================================
echo 1. Android APK (Release)
echo 2. Android App Bundle (AAB)
echo 3. Web (Release)
echo 4. Windows (Release)
echo 5. Todos os builds
echo.

set /p choice="Escolha uma opcao (1-5): "

if "%choice%"=="1" goto build_apk
if "%choice%"=="2" goto build_aab
if "%choice%"=="3" goto build_web
if "%choice%"=="4" goto build_windows
if "%choice%"=="5" goto build_all
goto invalid

:build_apk
echo.
echo Construindo APK para Android...
flutter build apk --release
echo APK criado em: build\app\outputs\flutter-apk\app-release.apk
goto end

:build_aab
echo.
echo Construindo App Bundle para Android...
flutter build appbundle --release
echo AAB criado em: build\app\outputs\bundle\release\app-release.aab
goto end

:build_web
echo.
echo Construindo para Web...
flutter build web --release
echo Build web criado em: build\web\
goto end

:build_windows
echo.
echo Construindo para Windows...
flutter build windows --release
echo Build Windows criado em: build\windows\x64\runner\Release\
goto end

:build_all
echo.
echo Construindo todos os builds...
echo.
echo 1/4 - Construindo APK...
flutter build apk --release
echo.
echo 2/4 - Construindo App Bundle...
flutter build appbundle --release
echo.
echo 3/4 - Construindo Web...
flutter build web --release
echo.
echo 4/4 - Construindo Windows...
flutter build windows --release
echo.
echo Todos os builds concluidos!
goto end

:invalid
echo Opcao invalida!
goto end

:end
echo.
echo ========================================
echo   Build Concluido!
echo ========================================
echo.
echo IMPORTANTE: Certifique-se de que o servidor
echo da API esteja rodando em 198.1.195.126:5032
echo antes de testar a aplicacao.
echo.
pause
