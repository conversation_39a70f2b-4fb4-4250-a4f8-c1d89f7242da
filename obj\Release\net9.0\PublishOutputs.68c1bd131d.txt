C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\ProjetoChamegoes.exe
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\appsettings.Development.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\appsettings.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\appsettings.Production.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\android\app\google-services.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\annotation_processor_list\debug\javaPreCompileDebug\annotationProcessors.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\assets\debug\mergeDebugAssets\flutter_assets\AssetManifest.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\assets\debug\mergeDebugAssets\flutter_assets\FontManifest.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\assets\debug\mergeDebugAssets\flutter_assets\NativeAssetsManifest.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\compatible_screen_manifest\debug\createDebugCompatibleScreenManifests\output-metadata.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\4p55645w\logs\arm64-v8a\build_model.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\4p55645w\logs\arm64-v8a\metadata_generation_record.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\4p55645w\logs\armeabi-v7a\build_model.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\4p55645w\logs\armeabi-v7a\metadata_generation_record.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\4p55645w\logs\x86\build_model.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\4p55645w\logs\x86\metadata_generation_record.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\4p55645w\logs\x86_64\build_model.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\4p55645w\logs\x86_64\metadata_generation_record.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\5c194s29\logs\arm64-v8a\build_model.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\5c194s29\logs\arm64-v8a\metadata_generation_record.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\5c194s29\logs\armeabi-v7a\build_model.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\5c194s29\logs\armeabi-v7a\metadata_generation_record.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\5c194s29\logs\x86_64\build_model.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\cxx\Debug\5c194s29\logs\x86_64\metadata_generation_record.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\flutter\debug\flutter_assets\AssetManifest.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\flutter\debug\flutter_assets\FontManifest.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\flutter\debug\flutter_assets\NativeAssetsManifest.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\linked_resources_binary_format\debug\processDebugResources\output-metadata.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_manifests\debug\processDebugManifest\output-metadata.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\mergeDebugResources.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-af.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-am.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ar.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-as.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-az.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-b+sr+Latn.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-be.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-bg.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-bn.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-bs.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ca.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-cs.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-da.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-de.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-el.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-en-rAU.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-en-rCA.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-en-rGB.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-en-rIN.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-en-rXC.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-es-rUS.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-es.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-et.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-eu.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-fa.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-fi.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-fr-rCA.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-fr.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-gl.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-gu.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-h720dp-v13.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-hdpi-v4.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-hi.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-hr.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-hu.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-hy.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-in.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-is.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-it.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-iw.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ja.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ka.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-kk.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-km.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-kn.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ko.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ky.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-land.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-large-v4.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ldltr-v21.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-lo.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-lt.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-lv.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-mk.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ml.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-mn.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-mr.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ms.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-my.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-nb.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ne.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-night-v8.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-nl.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-or.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-pa.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-pl.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-port.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-pt-rBR.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-pt-rPT.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-pt.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ro.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ru.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-si.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-sk.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-sl.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-sq.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-sr.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-sv.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-sw.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-sw360dp-v13.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-sw600dp-v13.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ta.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-te.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-th.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-tl.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-tr.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-uk.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-ur.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-uz.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-v16.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-v17.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-v18.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-v21.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-v22.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-v23.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-v24.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-v25.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-v26.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-v28.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-vi.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-watch-v20.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-watch-v21.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-xlarge-v4.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-zh-rCN.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-zh-rHK.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-zh-rTW.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values-zu.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2\values.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\single\mergeDebugResources.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\navigation_json\debug\extractDeepLinksDebug\navigation.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\packaged_manifests\debug\processDebugManifestForPackage\output-metadata.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\intermediates\signing_config_versions\debug\writeDebugSigningConfigVersions\signing-config-versions.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\app\outputs\apk\debug\output-metadata.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\firebase_core\intermediates\aapt_friendly_merged_manifests\debug\processDebugManifest\aapt\output-metadata.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\firebase_core\intermediates\annotation_processor_list\debug\javaPreCompileDebug\annotationProcessors.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\firebase_core\intermediates\navigation_json\debug\extractDeepLinksDebug\navigation.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\firebase_messaging\intermediates\aapt_friendly_merged_manifests\debug\processDebugManifest\aapt\output-metadata.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\firebase_messaging\intermediates\annotation_processor_list\debug\javaPreCompileDebug\annotationProcessors.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\firebase_messaging\intermediates\navigation_json\debug\extractDeepLinksDebug\navigation.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\fluttertoast\intermediates\aapt_friendly_merged_manifests\debug\processDebugManifest\aapt\output-metadata.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\fluttertoast\intermediates\annotation_processor_list\debug\javaPreCompileDebug\annotationProcessors.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\fluttertoast\intermediates\navigation_json\debug\extractDeepLinksDebug\navigation.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\flutter_local_notifications\intermediates\aapt_friendly_merged_manifests\debug\processDebugManifest\aapt\output-metadata.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\flutter_local_notifications\intermediates\annotation_processor_list\debug\javaPreCompileDebug\annotationProcessors.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\flutter_local_notifications\intermediates\navigation_json\debug\extractDeepLinksDebug\navigation.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\path_provider_android\intermediates\aapt_friendly_merged_manifests\debug\processDebugManifest\aapt\output-metadata.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\path_provider_android\intermediates\annotation_processor_list\debug\javaPreCompileDebug\annotationProcessors.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\path_provider_android\intermediates\navigation_json\debug\extractDeepLinksDebug\navigation.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\shared_preferences_android\intermediates\aapt_friendly_merged_manifests\debug\processDebugManifest\aapt\output-metadata.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\shared_preferences_android\intermediates\annotation_processor_list\debug\javaPreCompileDebug\annotationProcessors.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\build\shared_preferences_android\intermediates\navigation_json\debug\extractDeepLinksDebug\navigation.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\ios\Runner\Assets.xcassets\AppIcon.appiconset\Contents.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\ios\Runner\Assets.xcassets\LaunchImage.imageset\Contents.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\macos\Runner\Assets.xcassets\AppIcon.appiconset\Contents.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\chamego_virtual\web\manifest.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Config\firebase-service-account.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\ProjetoChamegoes.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\ProjetoChamegoes.deps.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\ProjetoChamegoes.runtimeconfig.json
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\ProjetoChamegoes.pdb
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Dapper.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\FirebaseAdmin.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Google.Api.Gax.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Google.Api.Gax.Rest.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Google.Apis.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Google.Apis.Auth.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Google.Apis.Core.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Microsoft.AspNetCore.OpenApi.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Microsoft.OpenApi.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Newtonsoft.Json.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\System.CodeDom.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\System.Data.SqlClient.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\System.Management.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\runtimes\win-arm64\native\sni.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\runtimes\win-x64\native\sni.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\runtimes\win-x86\native\sni.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\Users\<USER>\OneDrive\Desktop\projetos\ProjetoChamegoes\publish\runtimes\win\lib\net7.0\System.Management.dll
