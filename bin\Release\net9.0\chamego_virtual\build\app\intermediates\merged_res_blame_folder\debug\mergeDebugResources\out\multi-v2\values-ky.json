{"logs": [{"outputFile": "com.example.chamego_virtual.app-mergeDebugResources-48:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8c3eefca2d9a5f42152c30d1f581b28f\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ky\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "153", "endOffsets": "348"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4538", "endColumns": "157", "endOffsets": "4691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\21c8db8decf765cf7dd74ee06345bde5\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ky\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,440,565,668,809,932,1043,1148,1313,1416,1562,1688,1821,1981,2041,2097", "endColumns": "101,144,124,102,140,122,110,104,164,102,145,125,132,159,59,55,73", "endOffsets": "294,439,564,667,808,931,1042,1147,1312,1415,1561,1687,1820,1980,2040,2096,2170"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3551,3657,3806,3935,4042,4187,4314,4429,4696,4865,4972,5122,5252,5389,5553,5617,5677", "endColumns": "105,148,128,106,144,126,114,108,168,106,149,129,136,163,63,59,77", "endOffsets": "3652,3801,3930,4037,4182,4309,4424,4533,4860,4967,5117,5247,5384,5548,5612,5672,5750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2671cc70069c5b73b4048b0546895da7\\transformed\\appcompat-1.1.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,902,993,1085,1180,1274,1375,1468,1563,1658,1749,1840,1920,2026,2131,2229,2336,2442,2557,2718,2820", "endColumns": "110,108,111,84,104,116,78,78,90,91,94,93,100,92,94,94,90,90,79,105,104,97,106,105,114,160,101,80", "endOffsets": "211,320,432,517,622,739,818,897,988,1080,1175,1269,1370,1463,1558,1653,1744,1835,1915,2021,2126,2224,2331,2437,2552,2713,2815,2896"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,902,993,1085,1180,1274,1375,1468,1563,1658,1749,1840,1920,2026,2131,2229,2336,2442,2557,2718,6138", "endColumns": "110,108,111,84,104,116,78,78,90,91,94,93,100,92,94,94,90,90,79,105,104,97,106,105,114,160,101,80", "endOffsets": "211,320,432,517,622,739,818,897,988,1080,1175,1269,1370,1463,1558,1653,1744,1835,1915,2021,2126,2224,2331,2437,2552,2713,2815,6214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0ae860f2fb54c530241cb1bce335a021\\transformed\\preference-1.2.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,268,347,488,657,738", "endColumns": "70,91,78,140,168,80,78", "endOffsets": "171,263,342,483,652,733,812"}, "to": {"startLines": "54,55,56,57,60,61,62", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5755,5826,5918,5997,6320,6489,6570", "endColumns": "70,91,78,140,168,80,78", "endOffsets": "5821,5913,5992,6133,6484,6565,6644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\34ae8682f85f5cb1e3f80c2df56ac56d\\transformed\\core-1.13.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "29,30,31,32,33,34,35,59", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2820,2920,3022,3125,3232,3336,3440,6219", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "2915,3017,3120,3227,3331,3435,3546,6315"}}]}]}