# Testes da API em Produção
# Servidor: 198.1.195.126:5032

@baseUrl = http://198.1.195.126:5032

### 1. Teste de conectividade básica
GET {{baseUrl}}/
Accept: application/json

### 2. Verificar se é fim de semana (endpoint público)
GET {{baseUrl}}/api/utils/is-weekend
Accept: application/json

### 3. Obter número da semana
GET {{baseUrl}}/api/utils/week-number
Accept: application/json

### 4. Contagem regressiva para o fim de semana
GET {{baseUrl}}/api/utils/weekend-countdown
Accept: application/json

### 5. Swagger UI (deve redirecionar)
GET {{baseUrl}}/swagger

### 6. Swagger JSON
GET {{baseUrl}}/swagger/v1/swagger.json
Accept: application/json

### 7. Inicializar banco de dados (se necessário)
POST {{baseUrl}}/api/database/initialize
Content-Type: application/json

### 8. Registrar um novo usuário de teste
POST {{baseUrl}}/api/auth/register
Content-Type: application/json

{
  "username": "teste_producao",
  "email": "<EMAIL>",
  "password": "Teste123!",
  "displayName": "Usuário de Teste Produção"
}

### 9. Login com usuário de teste
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "username": "teste_producao",
  "password": "Teste123!"
}

### 10. Obter chamegos (requer autenticação)
# Substitua {token} pelo token obtido no login
GET {{baseUrl}}/api/chamegos
Authorization: Bearer {token}
Accept: application/json

### 11. Verificar disponibilidade de feedback
GET {{baseUrl}}/api/utils/feedback-availability
Accept: application/json

### 12. Health check personalizado
GET {{baseUrl}}/api/utils/current-date
Accept: application/json
