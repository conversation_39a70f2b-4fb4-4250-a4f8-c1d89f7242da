using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using ProjetoChamegoes.Data;
using ProjetoChamegoes.Data.Repositories;
using ProjetoChamegoes.Helpers;
using ProjetoChamegoes.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Configurar a conexão com o banco de dados
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
builder.Services.AddSingleton<IDbConnectionFactory>(new SqlConnectionFactory(connectionString!));

// Registrar os repositórios
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IFeedbackRepository, FeedbackRepository>();
builder.Services.AddScoped<IChamegoRepository, ChamegoRepository>();
builder.Services.AddScoped<IVirtualChamegoRepository, VirtualChamegoRepository>();
builder.Services.AddScoped<IMoodRepository, MoodRepository>();
builder.Services.AddScoped<IWeeklyVirtualChamegoCountRepository, WeeklyVirtualChamegoCountRepository>();

// Registrar o serviço de inicialização do banco de dados
builder.Services.AddScoped<DbInitializerService>();

// Registrar o serviço de notificações
builder.Services.AddScoped<INotificationService, NotificationService>();

// Configurar o JWT
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8
                .GetBytes(builder.Configuration.GetSection("AppSettings:Token").Value!)),
            ValidateIssuer = false,
            ValidateAudience = false
        };
    });

// Adicionar o JwtHelper como serviço
builder.Services.AddScoped<JwtHelper>();

// Adicionar CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

// Adicionar controladores
builder.Services.AddControllers();

// Configurar OpenAPI/Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Chamego Virtual API",
        Version = "v1",
        Description = "API para o aplicativo Chamego Virtual",
        Contact = new OpenApiContact
        {
            Name = "Desenvolvedor",
            Email = "<EMAIL>"
        }
    });

    // Configurar o Swagger para usar o JWT
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header usando o esquema Bearer. Exemplo: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

var app = builder.Build();

// O banco de dados não será inicializado automaticamente
// Será inicializado sob demanda através de um endpoint específico

// Configure the HTTP request pipeline.
// Habilitar Swagger em todos os ambientes (incluindo produção para facilitar testes)
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Chamego Virtual API v1");
    c.RoutePrefix = "swagger";
});

// Comentado para produção HTTP
// app.UseHttpsRedirection();

// Usar CORS
app.UseCors();

// Usar autenticação e autorização
app.UseAuthentication();
app.UseAuthorization();

// Mapear controladores
app.MapControllers();

// Redirecionar a rota raiz para o Swagger
app.MapGet("/", () => Results.Redirect("/swagger"));

app.Run();
