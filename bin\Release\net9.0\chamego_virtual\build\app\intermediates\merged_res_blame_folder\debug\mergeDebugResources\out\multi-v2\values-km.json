{"logs": [{"outputFile": "com.example.chamego_virtual.app-mergeDebugResources-48:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0ae860f2fb54c530241cb1bce335a021\\transformed\\preference-1.2.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,264,342,475,644,724", "endColumns": "68,89,77,132,168,79,76", "endOffsets": "169,259,337,470,639,719,796"}, "to": {"startLines": "54,55,56,57,60,61,62", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5735,5804,5894,5972,6289,6458,6538", "endColumns": "68,89,77,132,168,79,76", "endOffsets": "5799,5889,5967,6100,6453,6533,6610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\34ae8682f85f5cb1e3f80c2df56ac56d\\transformed\\core-1.13.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "29,30,31,32,33,34,35,59", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2793,2888,2991,3089,3189,3290,3402,6188", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "2883,2986,3084,3184,3285,3397,3509,6284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8c3eefca2d9a5f42152c30d1f581b28f\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-km\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "144", "endOffsets": "339"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4513", "endColumns": "148", "endOffsets": "4657"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\21c8db8decf765cf7dd74ee06345bde5\\transformed\\jetified-play-services-base-18.5.0\\res\\values-km\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,442,559,660,818,938,1055,1160,1314,1427,1594,1715,1856,2010,2070,2124", "endColumns": "97,150,116,100,157,119,116,104,153,112,166,120,140,153,59,53,72", "endOffsets": "290,441,558,659,817,937,1054,1159,1313,1426,1593,1714,1855,2009,2069,2123,2196"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3514,3616,3771,3892,3997,4159,4283,4404,4662,4820,4937,5108,5233,5378,5536,5600,5658", "endColumns": "101,154,120,104,161,123,120,108,157,116,170,124,144,157,63,57,76", "endOffsets": "3611,3766,3887,3992,4154,4278,4399,4508,4815,4932,5103,5228,5373,5531,5595,5653,5730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2671cc70069c5b73b4048b0546895da7\\transformed\\appcompat-1.1.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,882,973,1065,1157,1251,1352,1445,1540,1634,1725,1816,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,76,90,91,91,93,100,92,94,93,90,90,81,103,103,99,108,108,108,161,97,82", "endOffsets": "202,301,411,498,601,722,800,877,968,1060,1152,1246,1347,1440,1535,1629,1720,1811,1893,1997,2101,2201,2310,2419,2528,2690,2788,2871"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,882,973,1065,1157,1251,1352,1445,1540,1634,1725,1816,1898,2002,2106,2206,2315,2424,2533,2695,6105", "endColumns": "101,98,109,86,102,120,77,76,90,91,91,93,100,92,94,93,90,90,81,103,103,99,108,108,108,161,97,82", "endOffsets": "202,301,411,498,601,722,800,877,968,1060,1152,1246,1347,1440,1535,1629,1720,1811,1893,1997,2101,2201,2310,2419,2528,2690,2788,6183"}}]}]}