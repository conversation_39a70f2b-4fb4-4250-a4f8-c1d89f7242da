# PowerShell script para deploy da aplicação .NET
# Servidor: *************

param(
    [string]$ServerIP = "*************",
    [string]$Username = "Administrator",
    [string]$DeployPath = "C:\inetpub\wwwroot\ChamegoAPI",
    [string]$ServiceName = "ChamegoVirtualAPI"
)

Write-Host "=== Deploy da Aplicação Chamego Virtual API ===" -ForegroundColor Green
Write-Host "Servidor: $ServerIP" -ForegroundColor Yellow
Write-Host "Caminho de Deploy: $DeployPath" -ForegroundColor Yellow

# Verificar se a pasta publish existe
if (-not (Test-Path ".\publish")) {
    Write-Host "Erro: Pasta 'publish' não encontrada. Execute 'dotnet publish' primeiro." -ForegroundColor Red
    exit 1
}

Write-Host "Pasta publish encontrada. Preparando arquivos para deploy..." -ForegroundColor Green

# Criar arquivo de configuração do IIS
$webConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\ProjetoChamegoes.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
        </environmentVariables>
      </aspNetCore>
    </system.webServer>
  </location>
</configuration>
"@

# Salvar web.config na pasta publish
$webConfig | Out-File -FilePath ".\publish\web.config" -Encoding UTF8

Write-Host "Arquivo web.config criado para IIS." -ForegroundColor Green

# Instruções para deploy manual
Write-Host "`n=== INSTRUÇÕES PARA DEPLOY MANUAL ===" -ForegroundColor Cyan
Write-Host "1. Copie todo o conteúdo da pasta 'publish' para o servidor $ServerIP" -ForegroundColor White
Write-Host "2. Caminho sugerido no servidor: $DeployPath" -ForegroundColor White
Write-Host "3. Configure o IIS para apontar para esta pasta" -ForegroundColor White
Write-Host "4. Certifique-se de que o .NET 9.0 Runtime está instalado no servidor" -ForegroundColor White
Write-Host "5. Configure o Application Pool para usar 'No Managed Code'" -ForegroundColor White
Write-Host "6. A aplicação estará disponível em: http://$ServerIP" -ForegroundColor White

Write-Host "`n=== COMANDOS PARA EXECUTAR NO SERVIDOR ===" -ForegroundColor Cyan
Write-Host "# Instalar .NET 9.0 Runtime (se não estiver instalado):" -ForegroundColor Yellow
Write-Host "winget install Microsoft.DotNet.Runtime.9" -ForegroundColor Gray

Write-Host "`n# Criar pasta de deploy:" -ForegroundColor Yellow
Write-Host "mkdir $DeployPath" -ForegroundColor Gray

Write-Host "`n# Configurar IIS (executar como Administrador):" -ForegroundColor Yellow
Write-Host "Import-Module WebAdministration" -ForegroundColor Gray
Write-Host "New-WebSite -Name 'ChamegoVirtualAPI' -Port 5032 -PhysicalPath '$DeployPath'" -ForegroundColor Gray

Write-Host "`n=== TESTE DA APLICAÇÃO ===" -ForegroundColor Cyan
Write-Host "Após o deploy, teste a aplicação acessando:" -ForegroundColor White
Write-Host "http://$ServerIP:5032/swagger" -ForegroundColor Green

Write-Host "`nDeploy preparado com sucesso!" -ForegroundColor Green
