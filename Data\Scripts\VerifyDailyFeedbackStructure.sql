-- Script para verificar se a estrutura de feedback diário está correta
-- Execute após recriar o banco com CreateAllTables.sql

PRINT '=== VERIFICAÇÃO DA ESTRUTURA DE FEEDBACK DIÁRIO ===';
PRINT '';

-- Verificar se a tabela Feedbacks existe
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Feedbacks')
BEGIN
    PRINT '✓ Tabela Feedbacks encontrada';
    
    -- Verificar estrutura das colunas
    PRINT '';
    PRINT 'Estrutura da tabela Feedbacks:';
    SELECT 
        COLUMN_NAME as 'Coluna',
        DATA_TYPE as 'Tipo',
        IS_NULLABLE as 'Permite_NULL',
        CHARACTER_MAXIMUM_LENGTH as 'Tamanho_Max',
        COLUMN_DEFAULT as 'Valor_Padrao'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Feedbacks'
    ORDER BY ORDINAL_POSITION;
    
    PRINT '';
    
    -- Verificar colunas específicas do sistema diário
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'FeedbackDate')
    BEGIN
        PRINT '✓ Coluna FeedbackDate existe (sistema diário)';
    END
    ELSE
    BEGIN
        PRINT '✗ ERRO: Coluna FeedbackDate não encontrada!';
    END
    
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'HighlightOfDay')
    BEGIN
        PRINT '✓ Coluna HighlightOfDay existe';
    END
    ELSE
    BEGIN
        PRINT '✗ ERRO: Coluna HighlightOfDay não encontrada!';
    END
    
    -- Verificar se colunas antigas NÃO existem
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'WeekNumber')
    BEGIN
        PRINT '✓ Coluna WeekNumber não existe (correto para sistema diário)';
    END
    ELSE
    BEGIN
        PRINT '✗ AVISO: Coluna WeekNumber ainda existe (sistema antigo)';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'Year')
    BEGIN
        PRINT '✓ Coluna Year não existe (correto para sistema diário)';
    END
    ELSE
    BEGIN
        PRINT '✗ AVISO: Coluna Year ainda existe (sistema antigo)';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Feedbacks') AND name = 'HighlightOfWeek')
    BEGIN
        PRINT '✓ Coluna HighlightOfWeek não existe (correto para sistema diário)';
    END
    ELSE
    BEGIN
        PRINT '✗ AVISO: Coluna HighlightOfWeek ainda existe (sistema antigo)';
    END
    
    PRINT '';
    
    -- Verificar constraints
    PRINT 'Verificando constraints:';
    
    IF EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UQ_Feedbacks_UserDate')
    BEGIN
        PRINT '✓ Constraint única UQ_Feedbacks_UserDate existe (um feedback por dia por usuário)';
    END
    ELSE
    BEGIN
        PRINT '✗ ERRO: Constraint única UQ_Feedbacks_UserDate não encontrada!';
    END
    
    IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Feedbacks_Users')
    BEGIN
        PRINT '✓ Foreign key FK_Feedbacks_Users existe';
    END
    ELSE
    BEGIN
        PRINT '✗ ERRO: Foreign key FK_Feedbacks_Users não encontrada!';
    END
    
    -- Verificar check constraint para SatisfactionRating
    IF EXISTS (SELECT * FROM sys.check_constraints WHERE parent_object_id = OBJECT_ID('Feedbacks'))
    BEGIN
        PRINT '✓ Check constraint para SatisfactionRating existe';
    END
    ELSE
    BEGIN
        PRINT '✗ AVISO: Check constraint para SatisfactionRating não encontrada';
    END
    
    PRINT '';
    
    -- Verificar índices
    PRINT 'Verificando índices:';
    
    IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_UserId')
    BEGIN
        PRINT '✓ Índice IX_Feedbacks_UserId existe';
    END
    ELSE
    BEGIN
        PRINT '✗ ERRO: Índice IX_Feedbacks_UserId não encontrado!';
    END
    
    IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_FeedbackDate')
    BEGIN
        PRINT '✓ Índice IX_Feedbacks_FeedbackDate existe';
    END
    ELSE
    BEGIN
        PRINT '✗ ERRO: Índice IX_Feedbacks_FeedbackDate não encontrado!';
    END
    
    IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_UserId_FeedbackDate')
    BEGIN
        PRINT '✓ Índice IX_Feedbacks_UserId_FeedbackDate existe';
    END
    ELSE
    BEGIN
        PRINT '✗ ERRO: Índice IX_Feedbacks_UserId_FeedbackDate não encontrado!';
    END
    
    -- Verificar se índices antigos NÃO existem
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Feedbacks_WeekNumber_Year')
    BEGIN
        PRINT '✓ Índice antigo IX_Feedbacks_WeekNumber_Year não existe (correto)';
    END
    ELSE
    BEGIN
        PRINT '✗ AVISO: Índice antigo IX_Feedbacks_WeekNumber_Year ainda existe';
    END
    
    PRINT '';
    
    -- Mostrar todos os índices da tabela Feedbacks
    PRINT 'Todos os índices da tabela Feedbacks:';
    SELECT 
        i.name AS 'Nome_Indice',
        i.type_desc AS 'Tipo',
        i.is_unique AS 'Unico',
        STRING_AGG(c.name, ', ') AS 'Colunas'
    FROM sys.indexes i
    INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
    INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
    WHERE i.object_id = OBJECT_ID('Feedbacks')
    GROUP BY i.name, i.type_desc, i.is_unique
    ORDER BY i.name;
    
END
ELSE
BEGIN
    PRINT '✗ ERRO CRÍTICO: Tabela Feedbacks não encontrada!';
END

PRINT '';
PRINT '=== RESUMO ===';
PRINT 'Se você viu apenas ✓ (check marks), a estrutura está correta para o sistema diário.';
PRINT 'Se você viu ✗ (X marks), há problemas que precisam ser corrigidos.';
PRINT 'Se você viu avisos, pode haver restos do sistema antigo que devem ser removidos.';

-- Teste básico de inserção (será revertido)
PRINT '';
PRINT '=== TESTE DE INSERÇÃO ===';

BEGIN TRANSACTION;

BEGIN TRY
    -- Tentar inserir um feedback de teste
    INSERT INTO Feedbacks (UserId, SatisfactionRating, Comment, HighlightOfDay, ImprovementSuggestion, FeedbackDate)
    VALUES (1, 8, 'Teste de feedback diário', 'Destaque do dia teste', 'Sugestão teste', GETDATE());
    
    PRINT '✓ Inserção de teste bem-sucedida';
    
    -- Tentar inserir outro feedback para o mesmo usuário na mesma data (deve falhar)
    INSERT INTO Feedbacks (UserId, SatisfactionRating, Comment, FeedbackDate)
    VALUES (1, 7, 'Segundo feedback do dia', GETDATE());
    
    PRINT '✗ ERRO: Conseguiu inserir dois feedbacks no mesmo dia (constraint não está funcionando)';
    
END TRY
BEGIN CATCH
    IF ERROR_NUMBER() = 2627 -- Violação de constraint única
    BEGIN
        PRINT '✓ Constraint única funcionando corretamente (não permite dois feedbacks no mesmo dia)';
    END
    ELSE
    BEGIN
        PRINT '✗ Erro inesperado: ' + ERROR_MESSAGE();
    END
END CATCH

ROLLBACK TRANSACTION;
PRINT '✓ Teste revertido (nenhum dado foi mantido)';

PRINT '';
PRINT '=== VERIFICAÇÃO CONCLUÍDA ===';
