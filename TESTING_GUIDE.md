# Guia de Testes - Chamego Virtual API em Produção

## Informações do Servidor
- **URL Base**: http://*************:5032
- **Swagger UI**: http://*************:5032/swagger
- **Ambiente**: Produção

## Pré-requisitos para Teste

### 1. Verificar se o servidor está rodando
```bash
# Teste básico de conectividade
curl http://*************:5032/api/utils/is-weekend
```

### 2. Verificar se o banco de dados está configurado
- Acesse: http://*************:5032/swagger
- Execute o endpoint `/api/database/initialize` se necessário

## Testes da API

### Testes Básicos (Sem Autenticação)

1. **Health Check**
   ```
   GET http://*************:5032/api/utils/is-weekend
   ```

2. **Swagger UI**
   ```
   GET http://*************:5032/swagger
   ```

3. **Informações do Sistema**
   ```
   GET http://*************:5032/api/utils/week-number
   GET http://*************:5032/api/utils/weekend-countdown
   ```

### Testes de Autenticação

1. **Registrar Usuário**
   ```json
   POST http://*************:5032/api/auth/register
   {
     "username": "teste_producao",
     "email": "<EMAIL>", 
     "password": "Teste123!",
     "displayName": "Usuário Teste"
   }
   ```

2. **Login**
   ```json
   POST http://*************:5032/api/auth/login
   {
     "username": "teste_producao",
     "password": "Teste123!"
   }
   ```

### Testes com Autenticação

Use o token obtido no login para os próximos testes:

1. **Listar Chamegos**
   ```
   GET http://*************:5032/api/chamegos
   Authorization: Bearer {seu_token}
   ```

2. **Dados do Usuário**
   ```
   GET http://*************:5032/api/user/profile
   Authorization: Bearer {seu_token}
   ```

## Testes do Flutter App

### 1. Verificar Configuração
- Confirme que `app_config.dart` está configurado para produção
- Verifique se a URL está correta: `http://*************:5032/api`

### 2. Teste de Conectividade no App
1. Abra o app Flutter
2. Vá para a tela de configurações/debug (se disponível)
3. Execute o teste de conectividade
4. Verifique se retorna sucesso

### 3. Teste de Funcionalidades
1. **Registro de Usuário**
   - Crie uma nova conta
   - Verifique se o registro é bem-sucedido

2. **Login**
   - Faça login com as credenciais criadas
   - Verifique se o token é armazenado

3. **Funcionalidades Principais**
   - Teste todas as telas principais
   - Verifique se os dados são carregados corretamente
   - Teste envio de chamegos virtuais

## Troubleshooting

### Problemas Comuns

1. **Erro de Conexão**
   - Verifique se o servidor está rodando
   - Confirme se a porta 5032 está aberta
   - Teste com curl ou Postman

2. **Erro 500 (Internal Server Error)**
   - Verifique os logs do servidor
   - Confirme se o banco de dados está acessível
   - Execute `/api/database/initialize` se necessário

3. **Erro de CORS**
   - Verifique se o CORS está configurado corretamente
   - Confirme se `AllowAnyOrigin()` está ativo

4. **Erro de Autenticação**
   - Verifique se o token JWT está sendo enviado corretamente
   - Confirme se o token não expirou

### Comandos Úteis

```bash
# Verificar se a porta está aberta
telnet 198.**************

# Teste básico com curl
curl -v http://*************:5032/api/utils/is-weekend

# Teste de POST com curl
curl -X POST http://*************:5032/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"teste","password":"Teste123!"}'
```

## Checklist de Validação

- [ ] Servidor está rodando na porta 5032
- [ ] Swagger UI está acessível
- [ ] Endpoints públicos respondem corretamente
- [ ] Registro de usuário funciona
- [ ] Login retorna token válido
- [ ] Endpoints autenticados funcionam com token
- [ ] Flutter app conecta com sucesso
- [ ] Todas as funcionalidades do app funcionam
- [ ] Não há erros de CORS
- [ ] Performance está adequada

## Próximos Passos

Após validar todos os testes:
1. Configure monitoramento (opcional)
2. Configure backup do banco de dados
3. Documente credenciais de acesso
4. Configure SSL/HTTPS (se necessário)
5. Configure domínio personalizado (se necessário)
