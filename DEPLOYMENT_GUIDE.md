# Guia de Deploy - Chamego Virtual API

## Servidor de Des<PERSON>
- **IP**: *************
- **Porta**: 5032
- **Protocolo**: HTTP

## Opções de Deploy

### Opção 1: IIS (Recomendado para Windows Server)

#### Pré-requisitos
1. Windows Server com IIS instalado
2. .NET 9.0 Runtime instalado
3. ASP.NET Core Hosting Bundle

#### Passos para Deploy

1. **Instalar .NET 9.0 Runtime**
   ```powershell
   # Baixar e instalar do site oficial da Microsoft
   # Ou usar winget (se disponível):
   winget install Microsoft.DotNet.Runtime.9
   ```

2. **Criar pasta de deploy**
   ```powershell
   mkdir C:\inetpub\wwwroot\ChamegoAPI
   ```

3. **Copiar arquivos**
   - Copie todo o conteúdo da pasta `publish` para `C:\inetpub\wwwroot\ChamegoAPI`

4. **Configurar IIS**
   ```powershell
   # Executar como Administrador
   Import-Module WebAdministration
   New-WebSite -Name "ChamegoVirtualAPI" -Port 5032 -PhysicalPath "C:\inetpub\wwwroot\ChamegoAPI"
   ```

5. **Configurar Application Pool**
   - Definir .NET CLR Version como "No Managed Code"
   - Definir Identity como ApplicationPoolIdentity

### Opção 2: Kestrel Standalone (Mais Simples)

#### Passos para Deploy

1. **Copiar arquivos para o servidor**
   - Copie a pasta `publish` para o servidor (ex: `C:\ChamegoAPI`)

2. **Executar a aplicação**
   ```cmd
   cd C:\ChamegoAPI
   set ASPNETCORE_ENVIRONMENT=Production
   dotnet ProjetoChamegoes.dll
   ```

3. **Criar serviço Windows (Opcional)**
   ```powershell
   # Instalar como serviço Windows
   sc create "ChamegoVirtualAPI" binPath="C:\ChamegoAPI\ProjetoChamegoes.exe" start=auto
   sc start "ChamegoVirtualAPI"
   ```

## Configuração de Firewall

Certifique-se de que a porta 5032 está aberta:

```powershell
# Windows Firewall
New-NetFirewallRule -DisplayName "Chamego API" -Direction Inbound -Port 5032 -Protocol TCP -Action Allow
```

## Verificação do Deploy

Após o deploy, teste a aplicação:

1. **Swagger UI**: http://*************:5032/swagger
2. **Health Check**: http://*************:5032/api/utils/is-weekend
3. **API Base**: http://*************:5032/api

## Configuração do Banco de Dados

A aplicação está configurada para conectar ao SQL Server em:
- **Servidor**: *************
- **Database**: ChamegoDB
- **Usuário**: sa
- **Senha**: Jeanderson32

Certifique-se de que:
1. O SQL Server está rodando
2. O banco ChamegoDB existe
3. As tabelas foram criadas (use o endpoint `/api/database/initialize`)

## Logs e Troubleshooting

### Logs do IIS
- Localização: `C:\inetpub\logs\LogFiles`

### Logs da Aplicação
- A aplicação logará no Event Viewer do Windows

### Comandos Úteis
```powershell
# Verificar se a aplicação está rodando
netstat -an | findstr :5032

# Reiniciar IIS
iisreset

# Verificar logs do Event Viewer
Get-EventLog -LogName Application -Source "IIS AspNetCore Module V2" -Newest 10
```

## Próximos Passos

Após o deploy bem-sucedido:
1. Configure o Flutter app para usar a URL de produção
2. Teste todas as funcionalidades
3. Configure backup do banco de dados
4. Configure monitoramento (opcional)
